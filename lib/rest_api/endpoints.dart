import 'package:flutter/foundation.dart' show immutable;

/// Endpoints for the API
@immutable
final class EndPoints {
  const EndPoints._();

  /// Endpoint for getting all videos
  static const String getAllVideos = 'post/get_all_videos';

  /// Endpoint to check if user already exists or not during login
  static const String checkUserExists = 'users/user/check_user_exists';

  /// Endpoint to update user information
  static const String updateUserInformation = 'users/user/update_user';

  /// Endpoint to send verification code to user's email or mobile number
  static const String sendVerificationCode = 'users/authenticate/send_otp';

  /// Endpoint to check entered username valid or not
  static const String checkUniqueUserName = 'users/user/check_unique_username';

  /// Endpoint to get list of searched user data
  static const String searchUserList = 'search/search_user';

  /// Endpoint to get search suggestion list
  static const String searchSuggestionList = 'search/search_suggestion';

  /// Endpoint to verify 4 digit code
  static const String verifyOtp = 'users/authenticate/verify_otp';

  /// Endpoint to create account
  static const String signUpApi = 'users/authenticate/signup';

  /// Endpoint to login
  static const String signInApi = 'users/authenticate/login';

  /// Endpoint to reset password
  static const String resetPasswordApi = 'users/authenticate/reset_password';

  /// Endpoint to fetch user details
  static const String getUserDetails = 'users/user/get_user_details';

  /// Endpoint to create community
  static const String createCommunity = 'communities/create_community';

  /// Endpoints to get community details
  static const String getCommunityDetails = 'communities/get_community_details';

  /// Endpoints to update community
  static const String updateCommunity = 'communities/update_community';

  /// Endpoint to create signed url
  static const String createSignedUrl = 'medias/aws/get_signed_url';

  /// to get spotlight videos
  static const String spotlight = 'spotlight_feeds';

  /// to get following videos
  static const String followingPosts = 'following_feeds';

  /// to Get Comment List
  static const String getCommentList = 'posts/comment_list';

  /// to post Comment
  static const String postComment = 'posts/post_comment';

  ///to comment Like Unlike
  static const String postCommentLike = 'posts/like_comment';

  /// Endpoints to get communityTopics
  static const String communityTopics = 'communities/topics';

  /// Endpoint to get own community list
  static const String profileOwnCommunityList = 'communities/profile_own_community_list';

  /// Endpoints to get new discoveries
  static const String newDiscoveries = 'communities/discover/recent';

  /// Endpoint to request follow/request or unfollow
  static const String followUnfollow = 'users/user/follow_unfollow';

  /// Endpoints to get new discoveries
  static const String topicsDiscoveries = 'communities/discover/topics';

  /// Endpoint to request block specific user
  static const String blockUser = 'users/user/block_user';

  /// Endpoint to request unblock specific user
  static const String unblockUser = 'users/user/unblock_user';

  /// Endpoint to request suggested users
  static const String userSuggestion = 'users/user/user_suggestion';

  /// Endpoint to request following list of users.
  static const String followingList = 'users/user/following_list';

  /// Endpoint to request follower list of users.
  static const String followerList = 'users/user/followers_list';

  /// Endpoints to get nearby community
  static const String nearByCommunity = 'communities/discover/near_by';

  /// Endpoint to request follower list of users.
  static const String pendingRequest = 'users/user/pending_requests';

  /// Endpoint to request for communities by topic
  static const String communitiesByTopic = 'communities/discover/topic_by_communities';

  /// Endpoint to update multi action community
  ///
  static const String multiActionCommunities = 'communities/multi_action';

  /// Endpoint to request for contact sync
  static const String syncContacts = 'users/user/sync_contacts';

  /// Endpoint to request to get user profile request
  static const String getUserProfileStatus = 'users/user/get_user_status';

  ///Endpoint to add community rule
  static const String addCommunityRule = 'communities/add_rules';

  ///Endpoint to delete community rule
  static const String deleteCommunityRule = 'communities/delete_rules';

  ///Endpoint to update community rule
  static const String updateCommunityRule = 'communities/update_rules';

  /// Endpoint to request for popular community
  static const String popularCommunity = 'communities/discover/popular';

  /// Endpoint to request for get community member
  static const String getCommunityMember = 'communities/member_user_list';

  /// Endpoint to request for get following user
  static const String getFollowingUser = 'communities/following_list_to_add_member';

  /// Endpoint to request to add Member in Community
  static const String addMemberToCommunity = 'communities/add_member';

  /// Endpoint to request to requested members for community
  static const String requestedMemberList = 'communities/request_list';

  /// Endpoint to request to blocked members for community
  static const String blockedMemberList = 'communities/block_user_list';

  /// Endpoint to accept or reject requests
  static const String multiAction = 'communities/multi_action';

  /// Endpoint to request for join community
  static const String joinCommunity = 'communities/join_community';

  /// Endpoint to request for user settings
  static const String getUserSettings = 'users/settings/get_app_settings';

  /// Endpoint to update user settings
  static const String updateUserSettings = 'users/settings/update_app_settings';

  /// Endpoint to request for change Password
  static const String changePassword = 'users/authenticate/change_password';

  /// Endpoint to request for list of joined community
  static const String getJoinedCommunities = 'communities/get_joined_community_list';

  /// Endpoint to request for update joined community notification
  static const String updateCommunityNotificationSettings = 'communities/update_community_notification_settings';

  /// Endpoint to request for Preferred Languages list
  static const String getPreferredLanguageList = 'users/settings/get_preferred_language';

  /// Endpoint to request for add report
  static const String addReport = 'communities/add_report';

  /// Endpoint to request for get report reason list
  static const String getReportReasonList = 'communities/get_report_reason';

  /// Endpoint to request for list of blocked users
  static const String getBlockedUserList = 'users/user/get_block_users';

  /// Endpoint to request for deactivate account
  static const String deactivateAccount = 'users/user/deactivate_user';

  /// Endpoint to request for delete account
  static const String deleteAccount = 'users/settings/delete_user';

  /// Endpoint to request for get sister community
  static const String getSisterCommunity = 'communities/get_sister_community';

  ///  Endpoint to request for add sister community
  static const String addSisterCommunity = 'communities/add_sister_community';

  ///  Endpoint to request for get Added Sister Community List
  static const String getAddSisterCommunityList = 'communities/get_added_sister_community_list';

  /// Endpoint to remove sister Community
  static const String deleteSisterCommunity = 'communities/delete_sister_community';

  /// Endpoint to request for log
  static const String logOut = 'users/authenticate/logout';

  /// Endpoint to request follower and following list.
  static const String followerFollowingList = 'users/user/chat_user_list';

  /// Endpoint to get Community User Detail
  static const String getCommunityUserDetail = 'communities/get_user_details_for_community';

  /// Endpoint to request for get community member
  static const String getCommunitySilencedMember = 'communities/silenced_user_list';

  /// Endpoint to request for get mutual community list
  static const String getMutualCommunityList = 'communities/get_mutual_community_list';

  /// Endpoint to request for update user chat setting
  static const String updateContactChatSetting = 'users/user/save_user_chat_setting';

  /// Endpoint to request search community list
  static const String searchCommunity = 'search/search_community';

  /// Endpoint to request tag user list
  static const String tagUserList = 'posts/tag_users_list';

  /// Endpoint to create post
  static const String createPost = 'posts/create_post';

  ///Search User for Post
  static const String searchPostUser = 'users/user/search_tag_user';

  /// Endpoint to get users and community for sharing
  static const String getUsersCommunityForSharing = 'users/user/share_with_list';

  /// Endpoint to request link/unlink(edit) email or phone number
  static const String editEmailPhoneNumber = 'users/settings/link_unlink_email_phone';

  /// Endpoint to update User info
  static const String updateUserInfoAppleLogin = 'users/authenticate/save_apple_user_details';

  /// Endpoint to add member list
  static const String addMemberList = 'users/user/add_member_list';

  /// Endpoint to post number of shared count on posts
  static const String postShareCount = 'posts/share';

  /// Endpoint to Like UnLike SpotLight feed
  static const String postLikeUnLike = 'posts/post_like_unlike';

  /// Endpoint to Bookmark SpotLight feed
  static const String postBookmark = 'posts/bookmark_post';

  /// Endpoint to fetch video posts
  static const String videoPost = 'posts/user/videos';

  /// Endpoint to fetch capsule posts
  static const String capsulePost = 'posts/user/capsule';

  /// Endpoint to fetch bookmarked posts
  static const String bookmarkedPost = 'posts/user/saved';

  /// Endpoint to fetch liked posts
  static const String likedPost = 'posts/user/liked';

  /// Endpoint to request search post list
  static const String searchPost = 'search/search_post';

  /// Endpoint to request capsule post list
  static const String searchCapsule = 'search/search_capsule';

  /// Endpoint to Notify Spotlight
  static const String postNotify = 'posts/notify';

  /// Endpoint to Comment Delete
  static const String postCommentDelete = 'posts/delete_comment';

  /// Endpoint to mark not interested to content
  static const String markNotInterested = 'posts/mark_not_interested';

  /// Endpoint to post view duration
  static const String postView = 'posts/view';

  /// Endpoint to post delete
  static String postDelete(String postId) => 'posts/$postId';

  /// Endpoint to post History
  static const String postHistory = 'posts/user/post_view_history';

  /// Endpoint for get score
  static const String getScoreBoard = 'score/dashboard';

  /// Endpoint for get Single Post data
  static String fetchSinglePost(String postId) => 'posts/$postId';

  /// Endpoint for Update FCM Token
  static const String updateFCMToken = 'users/user/fcm_token';

  /// Endpoints to get community details for messages
  static const String communityInfo = 'communities/get_community_info';

  /// Endpoint for get score
  static const String getScoreLevelUp = 'score/level-up';

  /// Endpoints to get user info for messages
  static const String userInfo = 'users/user/user_info';

  /// Endpoint to show user's notification
  static const String userNotificationList = 'notifications/notification/list_notifications';

  /// Endpoint to get total unread notification
  static const String unreadNotifications = 'notifications/notification/unread_notifications_count';

  /// Endpoint to update total unread notification
  static const String updateUnreadNotifications = 'notifications/notification/read_notification';

  /// Endpoint to check Invitation Code
  static const String checkInvitationCode = 'users/user/check_invitation_code';

  /// Endpoint to request Invitation Code
  static const String requestInvitationCode = 'users/early-access/early-access-create';

  /// Endpoint to userPostNotification
  static const String userPostNotification = 'users/user/followed_user_post_notification';

  /// Endpoint to fetch chat user list
  static const String chatUserList = 'users/user/chat_user_list';

  /// Endpoint for get app usage
  static const String appUsage = 'score/post-app-usage-analytics';

  /// Endpoint for get Tag user for Community
  static const String postMentionUserList = 'communities/mention_user_list';

  /// Endpoint for remove/close community
  static const String removeCommunity = 'communities/remove_community';

  /// Endpoint for airdrop referral boost tiers
  static const String referralBoostTiers = 'inapp-airdrop/inapp-airdrop/referral_boost_tiers';

  /// Endpoint for airdrop activity tasks
  static const String activityTask = 'inapp-airdrop/inapp-airdrop/activity_task';

  /// Endpoint for airdrop activity tasks
  static const String airdropPoints = 'inapp-airdrop/inapp-airdrop/points';

  /// Endpoint for wallet create
  static const String userWallet = 'users/wallet';

  /// Endpoint to fetch campaign details
  static const String airdropCampaign = 'users/settings/campaign_status';

  /// Endpoint to update show time for campaign dialog
  static const String airdropNotificationSentTime = 'inapp-airdrop/inapp-airdrop/notification_sent_time';

  /// Endpoint to get feeds from all communities
  static const String communityPosts = 'community_feeds';

  /// Endpoint to link referral code
  static const String linkReferralCode = 'users/user/check_referral_code';

  /// Endpoint to get community members list to add in room
  static const String communityMembersList = 'communities/room/members_list';

  /// Endpoint to create room of community
  static const String createCommunityRoom = 'communities/room';

  /// Endpoint to get Community rooms list within which user can send the messages
  static String getShareInRoomsOfCommunity(String communityId) => 'communities/room/share_in_room/$communityId';

  /// Endpoint to get Community rooms members count only for private room
  static String getTotalRoomMembers(String communityId) => 'communities/room/users_count/$communityId';

  /// Endpoint to add community members to room
  static const String addMembersToRoom = 'communities/room/add_member';

  /// Endpoint to get room members list
  static const String roomMembersList = 'communities/room/users';

  /// Endpoint to remove member of room
  static const String removeRoomMember = 'communities/room/user';

  /// Endpoint to mute room
  static const String muteCommunityRoom = 'communities/room/mute';

  /// Endpoint of Get Tagged user of post
  static const String taggedUserList = 'posts/user/tagged';

  /// Endpoint to get possible admins list
  static const String possibleAdminList = 'communities/possible_admins';

  /// Endpoint to assign new admin of community
  static const String assignNewAdmin = 'communities/assign_new_admin';

  /// Endpoint to Get community wall post List
  static const String communityWallPostList = 'posts/user/community_wall';

  /// Endpoint to close room of community
  static const String closeCommunityRoom = 'communities/room/close';
}
